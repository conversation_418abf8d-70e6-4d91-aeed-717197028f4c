# 🚀 ExoStack

**ExoStack** is an open-source hybrid AI orchestration platform designed to connect, register, and distribute ML/LLM workloads across a decentralized cluster of edge, cloud, and local GPU devices. Inspired by [Exo](https://github.com/exo-explore/exo) and [dstack](https://github.com/dstackai/dstack), ExoStack combines the best of peer-based compute and centralized control.

---

## 🌐 Project Goals

- ⚡ **Edge + Cloud Orchestration**: Easily run AI models on edge devices (RPi, Jetson, laptops) or GPU servers.
- 📡 **Hub + Agent Architecture**: Central hub with REST API and agents that self-register.
- 🧠 **On-Device Inference**: Run lightweight models (e.g., TinyLlama, Mistral) locally.
- 🧪 **CLI and Web UI**: DevOps-style command-line tooling plus React-based dashboard.
- 🧩 **Composable System**: Agents, hub, CLI, and UI can all be extended or replaced.

---

## 📁 Directory Structure

```

exostack/
├── exo\_agent/       # Edge device runtime
├── exo\_hub/         # Central FastAPI server
├── exo\_cli/         # CLI to control stack
├── exo\_ui/          # React dashboard (optional)
├── shared/          # Shared constants, models
├── scripts/         # Dev tools
├── tests/           # Pytest test cases
├── docker-compose.yml
├── requirements.txt
├── pyproject.toml
├── .env.example
└── README.md

````

---

## 🧰 Tech Stack

| Layer     | Stack                          |
|-----------|--------------------------------|
| Backend   | Python, FastAPI, Redis         |
| Agent     | Python, Transformers, Torch    |
| CLI       | Typer (Python)                 |
| UI        | React, TailwindCSS, Vite       |
| Infra     | Docker, Docker Compose         |

---

## ⚙️ Setup Instructions

### 1️⃣ Clone and Install

```bash
git clone https://github.com/jitenkr2030/exostack.git
cd exostack
python3 -m venv venv && source venv/bin/activate
pip install -r requirements.txt
````

### 2️⃣ Start Hub Server (FastAPI)

```bash
uvicorn exo_hub.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3️⃣ Start Agent (on any edge device)

```bash
python exo_agent/agent.py
```

### 4️⃣ CLI Usage

```bash
python exo_cli/main.py nodes     # View registered nodes
python exo_cli/main.py infer     # Trigger inference (WIP)
```

### 5️⃣ UI Setup (Optional)

```bash
cd exo_ui
npm install
npm run dev
```

### 6️⃣ Docker Compose (Optional)

```bash
docker-compose up --build
```

---

## 🚦 API Overview (Hub)

| Method | Endpoint    | Description              |
| ------ | ----------- | ------------------------ |
| POST   | `/register` | Agent registration       |
| GET    | `/nodes`    | List registered agents   |
| POST   | `/infer`    | Run inference (future)   |
| GET    | `/status`   | Node/system health check |

---

## ✅ Todo / Roadmap

* [x] Agent registration
* [x] Hub + Agent base communication
* [ ] Distributed model execution
* [ ] Device health monitoring
* [ ] Web dashboard (React)
* [ ] CLI auto-deploy and management
* [ ] P2P inference handoff
* [ ] Kubernetes deployment support

---

## 🧪 Testing

```bash
pytest tests/
```

---

## 🤝 Contributing

1. Fork the repo
2. Create a new branch (`git checkout -b feat-new-feature`)
3. Commit changes (`git commit -m 'Add new feature'`)
4. Push to branch (`git push origin feat-new-feature`)
5. Open a Pull Request

---

## 📜 License

 © [license](https://github.com/Jitenderkumar2030/exostack/blob/main/LICENSE.md)

---

## 🌍 Credits & Inspirations

* [Exo by exo-explore](https://github.com/exo-explore/exo)
* [dstack by dstack.ai](https://github.com/dstackai/dstack)
* [TinyLlama, Mistral, Hugging Face](https://huggingface.co)

---

=======
# ExoStack - Distributed AI Orchestration Platform

ExoStack is an advanced hybrid AI orchestration platform designed to distribute ML/LLM workloads across edge, cloud, and local GPU devices. It provides seamless model deployment, distributed execution, and intelligent resource management.

## 🌟 Key Features

### 1. Distributed Model Execution
- **Model Sharding**: Automatically splits and distributes large models across nodes
- **Load Balancing**: Intelligent workload distribution based on node capabilities
- **Task Distribution**: Efficient task routing and execution management
- **Execution Tracking**: Real-time monitoring of distributed executions

### 2. Device Health Monitoring
- **Real-time Metrics**: CPU, Memory, GPU, and Disk usage tracking
- **Historical Data**: Metrics storage and trend analysis
- **Alert System**: Configurable thresholds and notifications
- **Resource Optimization**: Automatic resource usage optimization

### 3. Web Dashboard
- **Real-time Monitoring**: Live system metrics visualization
- **Node Management**: Interactive node control and configuration
- **Model Deployment**: User-friendly model deployment interface
- **Task Monitoring**: Real-time task execution tracking
- **System Management**: Comprehensive system control interface

### 4. CLI Auto-deploy
- **Automated Deployment**: Streamlined deployment process
- **Configuration Management**: YAML-based configuration with validation
- **Deployment Validation**: Pre and post-deployment checks
- **Rollback Capabilities**: Automatic rollback on deployment failures

### 5. P2P Inference Handoff
- **Node Discovery**: Automatic peer node discovery
- **Load Distribution**: Intelligent task handoff between nodes
- **Fallback Mechanism**: Automatic failover handling
- **Secure Communication**: Encrypted P2P data transfer

### 6. Kubernetes Integration
- **StatefulSet Support**: Reliable distributed model hosting
- **Auto-scaling**: Dynamic resource scaling based on demand
- **Persistent Storage**: Efficient model and data storage
- **Resource Management**: Comprehensive resource quota management

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- Node.js 16+
- Kubernetes cluster (for distributed deployment)
- NVIDIA GPU drivers (for GPU support)

### Installation

1. Clone the repository:

git clone https://github.com/yourusername/exostack.git
cd exostack


2. Install Python dependencies:

pip install -r requirements.txt


3. Install UI dependencies:

cd exo_ui
npm install


### Quick Start

1. Start the ExoHub:

python start_exostack.py hub


2. Deploy an agent:

python start_exostack.py agent --node-id agent1


3. Access the dashboard:

cd exo_ui
npm run dev


## 📚 Documentation

### Component Architecture

#### ExoHub
- Central coordination server
- Task distribution management
- System health monitoring
- API endpoint management

#### ExoAgent
- Model execution engine
- Resource monitoring
- P2P communication
- Health reporting

#### CLI Tool
- Deployment automation
- Configuration management
- System monitoring
- Rollback handling

#### Web Dashboard
- System monitoring
- Resource management
- Model deployment
- Task tracking

### Configuration

#### Basic Configuration

# config.yaml
hub:
  port: 8000
  metrics_port: 9000
  
agent:
  node_id: "agent1"
  hub_address: "http://localhost:8000"
  
models:
  cache_dir: "./model_cache"
  max_memory: "8GB"


#### Kubernetes Deployment

# k8s/deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: exostack-model
spec:
  replicas: 3
  ...


### API Reference

#### Model Management

# Deploy model
POST /api/models/deploy
{
  "model_name": "gpt2",
  "version": "1.0",
  "replicas": 3
}

# Run inference
POST /api/models/inference
{
  "model": "gpt2",
  "input": "Hello, world!"
}


#### System Management

# Get system status
GET /api/status/health

# Get metrics
GET /api/metrics


## 🛠 Development

### Running Tests

# Run all tests
pytest tests/

# Run specific test suite
pytest tests/test_system_integration.py

# Run UI tests
cd exo_ui
npm test


### Building for Production

# Build UI
cd exo_ui
npm run build

# Build Docker images
docker-compose build


## 🔧 Advanced Usage

### Distributed Model Deployment

# Deploy distributed model
exo deploy model --name gpt2 --shards 3 --memory-per-shard 4GB

# Monitor deployment
exo status model gpt2


### P2P Configuration

# p2p_config.yaml
handoff:
  enabled: true
  max_retries: 3
  timeout: 30s


### Custom Metrics

# Register custom metric
metrics.register(
    name="model_latency",
    type="gauge",
    description="Model inference latency"
)


## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Thanks to all contributors
- Built with support from the open-source community
- Inspired by various distributed systems and ML platforms

## 📞 Support

- GitHub Issues: For bug reports and feature requests
- Documentation: [Link to docs]
- Community Discord: [Link to Discord]

## 🔄 Updates and Releases

Check the [CHANGELOG.md](CHANGELOG.md) for detailed version history and updates.

