# ExoStack - Distributed AI Orchestration Platform

## 🚀 Features Implemented

ExoStack now includes comprehensive distributed AI capabilities with the following features:

### Phase 1: Full Inference Capability for Any Model ✅
- **Model Registry System**: YAML-based configuration with metadata for popular models
- **Auto-loading**: Automatic model selection and loading based on resource availability
- **Enhanced Agent Capabilities**: Detailed resource reporting and model compatibility checking
- **Memory Management**: Intelligent model caching and resource optimization

### Phase 2: GPU Agent Support ✅
- **GPU Detection**: Multi-method GPU detection (PyTorch CUDA, pynvml, nvidia-smi)
- **Resource-Aware Scheduling**: GPU memory, compute capability, and availability checking
- **Task Routing**: Intelligent task assignment based on GPU requirements
- **Health Monitoring**: Real-time GPU utilization and memory tracking

### Phase 3: Streaming Inference ✅
- **Server-Sent Events (SSE)**: Real-time token streaming via `/inference/stream`
- **JSON Streaming**: Alternative streaming format via `/inference/stream_json`
- **Hub Relay**: Hub-based streaming that routes to appropriate agents
- **Error Handling**: Robust error handling and connection management

### Bonus Phase: Fine-tuning Integration Preparation ✅
- **Axolotl Integration Framework**: Infrastructure for distributed fine-tuning
- **Checkpoint Tracking**: Job progress monitoring and checkpoint management
- **Resource Scheduling**: GPU-aware fine-tuning job scheduling
- **Configuration Management**: Comprehensive fine-tuning parameter handling

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ExoStack Hub  │    │  ExoStack Agent │    │  ExoStack Agent │
│                 │    │                 │    │                 │
│ • Task Routing  │◄──►│ • Model Loading │    │ • Model Loading │
│ • GPU Scheduler │    │ • GPU Detection │    │ • GPU Detection │
│ • Stream Relay  │    │ • Streaming     │    │ • Streaming     │
│ • Fine-tuning   │    │ • Health Monitor│    │ • Health Monitor│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Model Registry

The system includes pre-configured support for:

- **TinyLlama/TinyLlama-1.1B-Chat-v1.0** - Lightweight testing model
- **microsoft/phi-2** - Efficient 2.7B parameter model
- **mistralai/Mistral-7B-Instruct-v0.1** - High-quality 7B model
- **meta-llama/Llama-2-13b-chat-hf** - Large 13B model
- **microsoft/ESM3-open** - Protein language model
- **THUDM/glm-4-9b-chat** - Chinese-English bilingual model

Each model includes:
- Resource requirements (RAM, GPU memory)
- Quantization options (4bit, 8bit, GPTQ)
- Compatibility information
- Performance characteristics

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Start the Platform

```bash
# Start hub and one agent
python scripts/start_exostack.py

# Start with multiple agents
python scripts/start_exostack.py --agents 3

# Start and run tests
python scripts/start_exostack.py --test
```

### 3. Test the System

```bash
# Run comprehensive tests
python scripts/start_exostack.py --test-only

# Or run tests manually
python tests/test_exostack_features.py
```

## 🔧 API Endpoints

### Hub Endpoints
- `GET /health` - Hub health status
- `POST /tasks/create_gpu_aware` - Create GPU-aware tasks
- `POST /tasks/stream` - Stream inference through hub
- `GET /tasks/queue/status` - Queue status
- `GET /nodes` - List registered agents

### Agent Endpoints
- `GET /health` - Agent health status
- `GET /health/detailed` - Detailed health with GPU info
- `POST /inference` - Standard inference
- `POST /inference/stream` - Streaming inference (SSE)
- `POST /inference/stream_json` - Streaming inference (JSON)
- `GET /models/available` - Available models
- `POST /models/load` - Load specific model
- `GET /capabilities` - Agent capabilities

## 💡 Usage Examples

### Basic Inference
```bash
curl -X POST "http://localhost:8001/inference" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-001",
    "model": "auto",
    "input": "Hello, how are you?",
    "parameters": {"max_tokens": 50}
  }'
```

### Streaming Inference
```bash
curl -X POST "http://localhost:8001/inference/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "stream-001", 
    "model": "auto",
    "input": "Write a short story about",
    "parameters": {"max_tokens": 100}
  }'
```

### GPU-Aware Task Creation
```bash
curl -X POST "http://localhost:8000/tasks/create_gpu_aware" \
  -d "model=auto&input_text=Explain quantum computing&requires_gpu=true&min_gpu_memory_gb=4.0"
```

### Hub-Relayed Streaming
```bash
curl -X POST "http://localhost:8000/tasks/stream" \
  -d "model=auto&input_text=Tell me about AI&requires_gpu=false"
```

## 🧪 Testing

The comprehensive test suite validates:

- ✅ Hub and agent connectivity
- ✅ Model registry loading
- ✅ GPU detection and capabilities
- ✅ Basic inference functionality
- ✅ Streaming inference (SSE format)
- ✅ GPU-aware task scheduling
- ✅ Model loading/unloading
- ✅ Queue management
- ✅ Health monitoring

Run tests with:
```bash
python tests/test_exostack_features.py
```

## 🔍 Monitoring

### Health Endpoints
- Hub: `http://localhost:8000/health`
- Agent: `http://localhost:8001/health/detailed`

### Web Interface
- Hub API Docs: `http://localhost:8000/docs`
- Agent API Docs: `http://localhost:8001/docs`

## 🎯 Key Features Demonstrated

1. **Automatic Model Selection**: System chooses appropriate models based on available resources
2. **GPU-Aware Scheduling**: Tasks are routed to agents with suitable GPU capabilities
3. **Real-time Streaming**: Token-by-token streaming with proper error handling
4. **Resource Monitoring**: Continuous monitoring of CPU, RAM, and GPU utilization
5. **Fault Tolerance**: Graceful handling of agent failures and resource constraints
6. **Scalability**: Easy addition of new agents and models

## 🔧 Configuration

### Environment Variables
- `AGENT_ID`: Unique agent identifier
- `AGENT_PORT`: Agent port (default: 8001)
- `HUB_URL`: Hub URL for agent registration

### Model Registry
Edit `shared/config/model_registry.yaml` to add new models or modify configurations.

## 🚀 Next Steps

The platform is ready for:
1. **Production Deployment**: Add authentication, monitoring, and scaling
2. **Axolotl Integration**: Complete fine-tuning implementation
3. **WebSocket Support**: Add WebSocket streaming alongside SSE
4. **Model Optimization**: Add quantization and optimization features
5. **Distributed Training**: Implement multi-node training capabilities

## 📊 Performance

The system has been tested with:
- Multiple concurrent inference requests
- GPU memory management under load
- Streaming performance with various model sizes
- Agent registration and discovery
- Task queue management

All core features are functional and ready for further development!
