#!/bin/bash

# ExoStack Production Deployment Installer
# Supports Kubernetes, Docker Compose, and standalone deployment modes

set -e

# Configuration
EXOSTACK_VERSION="${EXOSTACK_VERSION:-latest}"
DEPLOYMENT_MODE="${DEPLOYMENT_MODE:-kubernetes}"
NAMESPACE="${NAMESPACE:-exostack}"
DOMAIN="${DOMAIN:-exostack.local}"
ENABLE_GPU="${ENABLE_GPU:-true}"
ENABLE_MONITORING="${ENABLE_MONITORING:-true}"
ENABLE_INGRESS="${ENABLE_INGRESS:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    case $DEPLOYMENT_MODE in
        "kubernetes")
            if ! command -v kubectl &> /dev/null; then
                log_error "kubectl is required for Kubernetes deployment"
                exit 1
            fi
            
            if ! kubectl cluster-info &> /dev/null; then
                log_error "Cannot connect to Kubernetes cluster"
                exit 1
            fi
            
            log_success "Kubernetes cluster connection verified"
            ;;
            
        "docker-compose")
            if ! command -v docker-compose &> /dev/null && ! command -v docker &> /dev/null; then
                log_error "Docker and Docker Compose are required"
                exit 1
            fi
            
            log_success "Docker environment verified"
            ;;
            
        "standalone")
            if ! command -v python3 &> /dev/null; then
                log_error "Python 3.8+ is required for standalone deployment"
                exit 1
            fi
            
            log_success "Python environment verified"
            ;;
            
        *)
            log_error "Invalid deployment mode: $DEPLOYMENT_MODE"
            log_info "Supported modes: kubernetes, docker-compose, standalone"
            exit 1
            ;;
    esac
}

# Generate configuration
generate_config() {
    log_info "Generating configuration..."
    
    # Create config directory
    mkdir -p config
    
    # Generate database password
    DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    
    # Generate JWT secret
    JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
    
    # Create environment file
    cat > config/.env << EOF
# ExoStack Configuration
EXOSTACK_VERSION=$EXOSTACK_VERSION
DEPLOYMENT_MODE=$DEPLOYMENT_MODE
DOMAIN=$DOMAIN

# Database Configuration
DATABASE_URL=************************************************/exostack
POSTGRES_PASSWORD=$DB_PASSWORD

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Security
JWT_SECRET=$JWT_SECRET

# Features
ENABLE_GPU=$ENABLE_GPU
ENABLE_MONITORING=$ENABLE_MONITORING
ENABLE_ALERTS=true

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Model Configuration
MODEL_CACHE_SIZE=10GB
DEFAULT_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0

# Performance
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=300
HEARTBEAT_INTERVAL=30

# Networking
HUB_PORT=8000
AGENT_PORT=8001
DASHBOARD_PORT=3000
EOF

    log_success "Configuration generated in config/.env"
}

# Deploy to Kubernetes
deploy_kubernetes() {
    log_info "Deploying to Kubernetes..."
    
    # Create namespace
    kubectl apply -f deployment/kubernetes/namespace.yaml
    
    # Update secrets with generated values
    kubectl create secret generic exostack-secrets \
        --namespace=$NAMESPACE \
        --from-literal=postgres-password="$DB_PASSWORD" \
        --from-literal=jwt-secret="$JWT_SECRET" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy database
    kubectl apply -f deployment/kubernetes/postgres.yaml
    kubectl apply -f deployment/kubernetes/redis.yaml
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres --namespace=$NAMESPACE --timeout=300s
    
    # Deploy ExoStack components
    kubectl apply -f deployment/kubernetes/exostack-hub.yaml
    kubectl apply -f deployment/kubernetes/exostack-agent.yaml
    
    # Deploy monitoring if enabled
    if [ "$ENABLE_MONITORING" = "true" ]; then
        kubectl apply -f deployment/kubernetes/monitoring.yaml
    fi
    
    # Deploy ingress if enabled
    if [ "$ENABLE_INGRESS" = "true" ]; then
        # Update ingress with domain
        sed "s/exostack.yourdomain.com/$DOMAIN/g" deployment/kubernetes/exostack-hub.yaml | kubectl apply -f -
    fi
    
    # Wait for deployments
    log_info "Waiting for deployments to be ready..."
    kubectl wait --for=condition=available deployment/exostack-hub --namespace=$NAMESPACE --timeout=300s
    kubectl wait --for=condition=ready pod -l app=exostack-agent --namespace=$NAMESPACE --timeout=300s
    
    log_success "Kubernetes deployment completed"
    
    # Show access information
    show_kubernetes_access_info
}

# Deploy with Docker Compose
deploy_docker_compose() {
    log_info "Deploying with Docker Compose..."
    
    # Generate docker-compose.yml
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: exostack
      POSTGRES_USER: exostack
      POSTGRES_PASSWORD: $DB_PASSWORD
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U exostack"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  exostack-hub:
    image: exostack/hub:$EXOSTACK_VERSION
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************************/exostack
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET=$JWT_SECRET
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./config:/app/config
    restart: unless-stopped

  exostack-agent-cpu:
    image: exostack/agent:$EXOSTACK_VERSION
    ports:
      - "8001:8001"
    environment:
      - HUB_URL=http://exostack-hub:8000
      - AGENT_TYPE=cpu
      - LOG_LEVEL=INFO
    depends_on:
      - exostack-hub
    volumes:
      - models_cache:/app/models_cache
    restart: unless-stopped
    deploy:
      replicas: 2

  dashboard:
    image: exostack/dashboard:$EXOSTACK_VERSION
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - exostack-hub
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  models_cache:
EOF

    # Add GPU agent if enabled
    if [ "$ENABLE_GPU" = "true" ]; then
        cat >> docker-compose.yml << EOF

  exostack-agent-gpu:
    image: exostack/agent:$EXOSTACK_VERSION
    ports:
      - "8002:8001"
    environment:
      - HUB_URL=http://exostack-hub:8000
      - AGENT_TYPE=gpu
      - LOG_LEVEL=INFO
    depends_on:
      - exostack-hub
    volumes:
      - models_cache:/app/models_cache
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
EOF
    fi

    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "ExoStack Hub is running"
    else
        log_warning "ExoStack Hub may not be ready yet"
    fi
    
    log_success "Docker Compose deployment completed"
    show_docker_access_info
}

# Deploy standalone
deploy_standalone() {
    log_info "Setting up standalone deployment..."
    
    # Create virtual environment
    python3 -m venv venv
    source venv/bin/activate
    
    # Install dependencies
    pip install -r requirements.txt
    
    # Set up database
    export DATABASE_URL="sqlite:///./exostack.db"
    python -m alembic upgrade head
    
    # Create systemd services
    create_systemd_services
    
    log_success "Standalone deployment setup completed"
    show_standalone_access_info
}

# Create systemd services for standalone deployment
create_systemd_services() {
    log_info "Creating systemd services..."
    
    # ExoStack Hub service
    sudo tee /etc/systemd/system/exostack-hub.service > /dev/null << EOF
[Unit]
Description=ExoStack Hub
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
Environment=PATH=$(pwd)/venv/bin
ExecStart=$(pwd)/venv/bin/python -m exo_hub.main
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # ExoStack Agent service
    sudo tee /etc/systemd/system/exostack-agent.service > /dev/null << EOF
[Unit]
Description=ExoStack Agent
After=network.target exostack-hub.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
Environment=PATH=$(pwd)/venv/bin
Environment=HUB_URL=http://localhost:8000
ExecStart=$(pwd)/venv/bin/python -m exo_agent.main
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable services
    sudo systemctl daemon-reload
    sudo systemctl enable exostack-hub exostack-agent
    sudo systemctl start exostack-hub exostack-agent
    
    log_success "Systemd services created and started"
}

# Show access information for different deployment modes
show_kubernetes_access_info() {
    echo
    log_success "ExoStack deployed successfully to Kubernetes!"
    echo
    echo "Access Information:"
    echo "  Namespace: $NAMESPACE"
    echo "  Hub API: http://$DOMAIN (if ingress enabled)"
    echo "  Dashboard: http://$DOMAIN/dashboard"
    echo
    echo "Useful commands:"
    echo "  kubectl get pods -n $NAMESPACE"
    echo "  kubectl logs -f deployment/exostack-hub -n $NAMESPACE"
    echo "  kubectl port-forward svc/exostack-hub 8000:8000 -n $NAMESPACE"
}

show_docker_access_info() {
    echo
    log_success "ExoStack deployed successfully with Docker Compose!"
    echo
    echo "Access Information:"
    echo "  Hub API: http://localhost:8000"
    echo "  Dashboard: http://localhost:3000"
    echo "  Database: localhost:5432"
    echo "  Redis: localhost:6379"
    echo
    echo "Useful commands:"
    echo "  docker-compose logs -f"
    echo "  docker-compose ps"
    echo "  docker-compose down"
}

show_standalone_access_info() {
    echo
    log_success "ExoStack standalone deployment completed!"
    echo
    echo "Access Information:"
    echo "  Hub API: http://localhost:8000"
    echo "  Database: ./exostack.db (SQLite)"
    echo
    echo "Service management:"
    echo "  sudo systemctl status exostack-hub"
    echo "  sudo systemctl status exostack-agent"
    echo "  sudo systemctl restart exostack-hub"
}

# Cleanup function
cleanup() {
    case $DEPLOYMENT_MODE in
        "kubernetes")
            kubectl delete namespace $NAMESPACE --ignore-not-found=true
            ;;
        "docker-compose")
            docker-compose down -v
            ;;
        "standalone")
            sudo systemctl stop exostack-hub exostack-agent
            sudo systemctl disable exostack-hub exostack-agent
            sudo rm -f /etc/systemd/system/exostack-*.service
            sudo systemctl daemon-reload
            ;;
    esac
    
    log_success "Cleanup completed"
}

# Main execution
main() {
    echo "ExoStack Production Deployment Installer"
    echo "========================================"
    echo
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode)
                DEPLOYMENT_MODE="$2"
                shift 2
                ;;
            --domain)
                DOMAIN="$2"
                shift 2
                ;;
            --namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            --no-gpu)
                ENABLE_GPU="false"
                shift
                ;;
            --no-monitoring)
                ENABLE_MONITORING="false"
                shift
                ;;
            --cleanup)
                cleanup
                exit 0
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo
                echo "Options:"
                echo "  --mode MODE          Deployment mode (kubernetes|docker-compose|standalone)"
                echo "  --domain DOMAIN      Domain name for ingress"
                echo "  --namespace NS       Kubernetes namespace"
                echo "  --no-gpu            Disable GPU support"
                echo "  --no-monitoring     Disable monitoring"
                echo "  --cleanup           Remove existing deployment"
                echo "  --help              Show this help"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "Deployment mode: $DEPLOYMENT_MODE"
    log_info "Domain: $DOMAIN"
    log_info "GPU support: $ENABLE_GPU"
    log_info "Monitoring: $ENABLE_MONITORING"
    echo
    
    # Execute deployment
    check_prerequisites
    generate_config
    
    case $DEPLOYMENT_MODE in
        "kubernetes")
            deploy_kubernetes
            ;;
        "docker-compose")
            deploy_docker_compose
            ;;
        "standalone")
            deploy_standalone
            ;;
    esac
}

# Run main function
main "$@"
