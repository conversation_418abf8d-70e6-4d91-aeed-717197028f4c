apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: exostack-agent-gpu
  namespace: exostack
  labels:
    app: exostack-agent
    component: agent
    type: gpu
spec:
  selector:
    matchLabels:
      app: exostack-agent
      type: gpu
  template:
    metadata:
      labels:
        app: exostack-agent
        component: agent
        type: gpu
    spec:
      containers:
      - name: exostack-agent
        image: exostack/agent:latest
        ports:
        - containerPort: 8001
          name: http
        env:
        - name: HUB_URL
          value: "http://exostack-hub:8000"
        - name: AGENT_TYPE
          value: "gpu"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: LOG_LEVEL
        - name: REGION
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['topology.kubernetes.io/region']
        - name: ZONE
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['topology.kubernetes.io/zone']
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
            nvidia.com/gpu: 1
          limits:
            memory: "4Gi"
            cpu: "2"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: models-cache
          mountPath: /app/models_cache
        - name: dev-shm
          mountPath: /dev/shm
        securityContext:
          privileged: true
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: models-cache
        hostPath:
          path: /var/lib/exostack/models
          type: DirectoryOrCreate
      - name: dev-shm
        emptyDir:
          medium: Memory
          sizeLimit: 2Gi
      nodeSelector:
        accelerator: nvidia-tesla-gpu
      tolerations:
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: exostack-agent-cpu
  namespace: exostack
  labels:
    app: exostack-agent
    component: agent
    type: cpu
spec:
  replicas: 3
  selector:
    matchLabels:
      app: exostack-agent
      type: cpu
  template:
    metadata:
      labels:
        app: exostack-agent
        component: agent
        type: cpu
    spec:
      containers:
      - name: exostack-agent
        image: exostack/agent:latest
        ports:
        - containerPort: 8001
          name: http
        env:
        - name: HUB_URL
          value: "http://exostack-hub:8000"
        - name: AGENT_TYPE
          value: "cpu"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: LOG_LEVEL
        - name: REGION
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['topology.kubernetes.io/region']
        - name: ZONE
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['topology.kubernetes.io/zone']
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        volumeMounts:
        - name: models-cache
          mountPath: /app/models_cache
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8001
          initialDelaySeconds: 15
          periodSeconds: 10
      volumes:
      - name: models-cache
        emptyDir:
          sizeLimit: 10Gi
      nodeSelector:
        kubernetes.io/arch: amd64
---
apiVersion: v1
kind: Service
metadata:
  name: exostack-agent
  namespace: exostack
  labels:
    app: exostack-agent
    component: agent
spec:
  ports:
  - port: 8001
    targetPort: 8001
    protocol: TCP
    name: http
  selector:
    app: exostack-agent
  type: ClusterIP
  clusterIP: None  # Headless service for agent discovery
