apiVersion: apps/v1
kind: Deployment
metadata:
  name: exostack-hub
  namespace: exostack
  labels:
    app: exostack-hub
    component: hub
spec:
  replicas: 2
  selector:
    matchLabels:
      app: exostack-hub
  template:
    metadata:
      labels:
        app: exostack-hub
        component: hub
    spec:
      containers:
      - name: exostack-hub
        image: exostack/hub:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: REDIS_URL
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: LOG_LEVEL
        - name: ENABLE_METRICS
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: ENABLE_METRICS
        - name: ENABLE_ALERTS
          valueFrom:
            configMapKeyRef:
              name: exostack-config
              key: ENABLE_ALERTS
        - name: REGION
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['topology.kubernetes.io/region']
        - name: ZONE
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['topology.kubernetes.io/zone']
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: exostack-config
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
---
apiVersion: v1
kind: Service
metadata:
  name: exostack-hub
  namespace: exostack
  labels:
    app: exostack-hub
    component: hub
spec:
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: exostack-hub
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: exostack-hub-ingress
  namespace: exostack
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - exostack.yourdomain.com
    secretName: exostack-tls
  rules:
  - host: exostack.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: exostack-hub
            port:
              number: 8000
