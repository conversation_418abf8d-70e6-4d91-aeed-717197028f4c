apiVersion: v1
kind: Namespace
metadata:
  name: exostack
  labels:
    name: exostack
    app.kubernetes.io/name: exostack
    app.kubernetes.io/version: "1.0.0"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: exostack-config
  namespace: exostack
data:
  DATABASE_URL: "********************************************/exostack"
  REDIS_URL: "redis://redis:6379/0"
  LOG_LEVEL: "INFO"
  ENABLE_METRICS: "true"
  ENABLE_ALERTS: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: exostack-secrets
  namespace: exostack
type: Opaque
data:
  # Base64 encoded values
  postgres-password: ZXhvc3RhY2s=  # exostack
  redis-password: ""
  smtp-password: ""
  webhook-token: ""
