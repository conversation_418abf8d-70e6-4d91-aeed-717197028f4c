# ExoStack Hub Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY exo_hub/ ./exo_hub/
COPY shared/ ./shared/
COPY pyproject.toml .

# Create necessary directories
RUN mkdir -p /app/logs /app/models /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV HUB_HOST=0.0.0.0
ENV HUB_PORT=8000

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/status/health || exit 1

# Run the hub
CMD ["python", "-m", "uvicorn", "exo_hub.main:app", "--host", "0.0.0.0", "--port", "8000"]
