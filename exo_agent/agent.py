import asyncio
import logging
import time
import requests
from datetime import datetime
from fastapi import FastAP<PERSON>
from .health_monitor import health_monitor
from .executor import inference_engine
from shared.config.env import AGENT_ID, HUB_URL, AGENT_HOST, AGENT_PORT

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="ExoStack Agent", version="1.0.0")

@app.get("/health")
async def health_check():
    return {"status": "healthy", "agent_id": AGENT_ID}

@app.get("/health/detailed")
async def get_detailed_health():
    return health_monitor.get_detailed_health()

@app.get("/metrics/detailed")
async def get_detailed_metrics():
    return health_monitor.get_detailed_health()

@app.post("/tasks/execute")
async def execute_task(task_data: dict):
    task_id = task_data.get("id")
    start_time = time.time()
    
    try:
        result = await inference_engine.process_task(task_data)
        duration = time.time() - start_time
        health_monitor.record_task(task_id, "completed", duration)
        return {"status": "completed", "result": result}
        
    except Exception as e:
        duration = time.time() - start_time
        health_monitor.record_task(task_id, "failed", duration)
        return {"status": "failed", "error": str(e)}

@app.get("/ping")
async def ping():
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

def register_agent(agent_id: str, hub_url: str) -> bool:
    try:
        response = requests.post(
            f"{hub_url}/nodes/register",
            json={
                "id": agent_id,
                "host": AGENT_HOST,
                "port": AGENT_PORT,
                "capabilities": ["inference", "text-generation"],
                "max_concurrent_tasks": 5
            },
            timeout=10
        )
        
        if response.status_code == 200:
            logger.info(f"Agent {agent_id} registered successfully")
            return True
        else:
            logger.error(f"Registration failed: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Registration error: {e}")
        return False

def heartbeat(agent_id: str, hub_url: str) -> bool:
    try:
        response = requests.post(
            f"{hub_url}/nodes/{agent_id}/heartbeat",
            json={"timestamp": datetime.now().isoformat()},
            timeout=5
        )
        return response.status_code == 200
        
    except Exception as e:
        logger.debug(f"Heartbeat failed: {e}")
        return False

def run_inference():
    # Placeholder for inference logic
    pass

def main_loop():
    logger.info(f"Starting ExoStack Agent {AGENT_ID}")
    
    # Register with hub
    if not register_agent(AGENT_ID, HUB_URL):
        logger.error("Failed to register with hub, exiting...")
        return
    
    consecutive_failures = 0
    max_consecutive_failures = 5
    
    while True:
        try:
            logger.debug("Sending heartbeat...")
            if heartbeat(AGENT_ID, HUB_URL):
                consecutive_failures = 0
            else:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"{consecutive_failures} consecutive heartbeat failures, re-registering...")
                    register_agent(AGENT_ID, HUB_URL)
                    consecutive_failures = 0
            
            logger.debug("Running inference...")
            run_inference()
            
        except KeyboardInterrupt:
            logger.info("Received shutdown signal, exiting gracefully...")
            break
        except Exception as e:
            logger.error(f"Unexpected error in main loop: {e}")
            
        time.sleep(10)

if __name__ == "__main__":
    main_loop()