"""
Enhanced Agent Health Monitoring System with GPU support and real-time metrics
"""

import psutil
import time
import logging
import torch
import platform
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass
from threading import Thread, Lock
from queue import Queue
import GPUtil

logger = logging.getLogger(__name__)

@dataclass
class MetricSample:
    timestamp: float
    value: float
    metric_type: str

class MetricsBuffer:
    def __init__(self, max_samples: int = 100):
        self.max_samples = max_samples
        self.samples: Dict[str, List[MetricSample]] = {}
        self.lock = Lock()

    def add_sample(self, metric_type: str, value: float):
        with self.lock:
            if metric_type not in self.samples:
                self.samples[metric_type] = []
            
            self.samples[metric_type].append(
                MetricSample(
                    timestamp=time.time(),
                    value=value,
                    metric_type=metric_type
                )
            )
            
            # Keep only recent samples
            if len(self.samples[metric_type]) > self.max_samples:
                self.samples[metric_type] = self.samples[metric_type][-self.max_samples:]

    def get_metrics(self, metric_type: str) -> List[MetricSample]:
        with self.lock:
            return self.samples.get(metric_type, [])

class HealthMonitor:
    def __init__(self, collection_interval: float = 1.0):
        self.start_time = time.time()
        self.collection_interval = collection_interval
        self.metrics_buffer = MetricsBuffer()
        self.task_history: List[Dict] = []
        self.max_history = 1000
        self.is_collecting = False
        self.collection_thread: Optional[Thread] = None
        self.gpu_enabled = torch.cuda.is_available()
        
        # Initialize GPU monitoring if available
        self.gpus = []
        if self.gpu_enabled:
            try:
                self.gpus = GPUtil.getGPUs()
                logger.info(f"Initialized GPU monitoring for {len(self.gpus)} GPUs")
            except Exception as e:
                logger.warning(f"Failed to initialize GPU monitoring: {e}")

    def start_collection(self):
        """Start the metrics collection thread"""
        if not self.is_collecting:
            self.is_collecting = True
            self.collection_thread = Thread(target=self._collect_metrics, daemon=True)
            self.collection_thread.start()
            logger.info("Started health metrics collection")

    def stop_collection(self):
        """Stop the metrics collection thread"""
        self.is_collecting = False
        if self.collection_thread:
            self.collection_thread.join()
            logger.info("Stopped health metrics collection")

    def _collect_metrics(self):
        """Continuously collect metrics at the specified interval"""
        while self.is_collecting:
            try:
                # Collect CPU metrics
                cpu_percent = psutil.cpu_percent(interval=None)
                self.metrics_buffer.add_sample("cpu_usage", cpu_percent)

                # Collect memory metrics
                memory = psutil.virtual_memory()
                self.metrics_buffer.add_sample("memory_usage", memory.percent)
                self.metrics_buffer.add_sample("memory_available", memory.available / 1024 / 1024)  # MB

                # Collect GPU metrics if available
                if self.gpu_enabled:
                    for gpu_id, gpu in enumerate(self.gpus):
                        try:
                            gpu.load = GPUtil.getGPUs()[gpu_id].load * 100
                            gpu.memoryUtil = GPUtil.getGPUs()[gpu_id].memoryUtil * 100
                            self.metrics_buffer.add_sample(f"gpu_{gpu_id}_usage", gpu.load)
                            self.metrics_buffer.add_sample(f"gpu_{gpu_id}_memory", gpu.memoryUtil)
                        except Exception as e:
                            logger.warning(f"Failed to collect GPU metrics: {e}")

                # Add more metrics as needed

            except Exception as e:
                logger.error(f"Error collecting metrics: {e}")

    def get_detailed_health(self) -> Dict[str, Any]:
        return {
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": time.time() - self.start_time,
            "system": self._get_system_info(),
            "resources": self._get_resource_usage(),
            "tasks": self._get_task_stats(),
            "status": self._get_overall_status()
        }
        
    def _get_system_info(self) -> Dict[str, Any]:
        return {
            "cpu_count": psutil.cpu_count(),
            "cpu_count_logical": psutil.cpu_count(logical=True),
            "memory_total": psutil.virtual_memory().total,
            "boot_time": psutil.boot_time()
        }
        
    def _get_resource_usage(self) -> Dict[str, Any]:
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_usage": psutil.cpu_percent(interval=1),
            "memory_usage": memory.percent,
            "memory_total": memory.total,
            "memory_available": memory.available,
            "memory_used": memory.used,
            "disk_usage": disk.percent,
            "disk_total": disk.total,
            "disk_free": disk.free
        }
        
    def _get_task_stats(self) -> Dict[str, Any]:
        if not self.task_history:
            return {
                "total_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "avg_duration": 0.0,
                "success_rate": 0.0
            }
            
        completed = [t for t in self.task_history if t.get("status") == "completed"]
        failed = [t for t in self.task_history if t.get("status") == "failed"]
        
        avg_duration = 0.0
        if completed:
            total_duration = sum(t.get("duration", 0) for t in completed)
            avg_duration = total_duration / len(completed)
            
        success_rate = len(completed) / len(self.task_history) * 100 if self.task_history else 0
        
        return {
            "total_tasks": len(self.task_history),
            "completed_tasks": len(completed),
            "failed_tasks": len(failed),
            "avg_duration": avg_duration,
            "success_rate": success_rate
        }
        
    def _get_overall_status(self) -> str:
        try:
            cpu_usage = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            
            if cpu_usage > 90 or memory_usage > 90:
                return "critical"
            elif cpu_usage > 70 or memory_usage > 70:
                return "warning"
            else:
                return "healthy"
        except Exception:
            return "unknown"
            
    def record_task(self, task_id: str, status: str, duration: float = 0.0):
        task_record = {
            "task_id": task_id,
            "status": status,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        self.task_history.append(task_record)
        
        if len(self.task_history) > self.max_history:
            self.task_history = self.task_history[-self.max_history:]

health_monitor = HealthMonitor()