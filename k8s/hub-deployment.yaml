apiVersion: apps/v1
<<<<<<< HEAD
kind: Deployment
=======
kind: StatefulSet
>>>>>>> 4726b7b (update code for improvement)
metadata:
  name: exostack-hub
  namespace: exostack
  labels:
    app: exostack-hub
    component: hub
spec:
<<<<<<< HEAD
  replicas: 2
=======
  serviceName: exostack-hub-headless
  replicas: 3
>>>>>>> 4726b7b (update code for improvement)
  selector:
    matchLabels:
      app: exostack-hub
  template:
    metadata:
      labels:
        app: exostack-hub
<<<<<<< HEAD
    spec:
      containers:
      - name: exostack-hub
        image: exostack/hub:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: exostack-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: exostack-secrets
              key: secret-key
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: models
          mountPath: /app/models
        livenessProbe:
          httpGet:
            path: /status/health
            port: 8000
=======
        component: hub
    spec:
      serviceAccountName: exostack-sa
      containers:
      - name: hub
        image: exostack/hub:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 9000
          name: metrics
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: exostack-config
        - secretRef:
            name: exostack-secrets
        resources:
          requests:
            cpu: "1"
            memory: "2Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        volumeMounts:
        - name: hub-data
          mountPath: /data
        - name: hub-config
          mountPath: /etc/exostack
        livenessProbe:
          httpGet:
            path: /health
            port: http
>>>>>>> 4726b7b (update code for improvement)
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
<<<<<<< HEAD
            path: /status/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      volumes:
      - name: logs
        emptyDir: {}
      - name: models
        persistentVolumeClaim:
          claimName: models-pvc
      initContainers:
      - name: wait-for-redis
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z redis-service 6379; do echo waiting for redis; sleep 2; done;']
=======
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: hub-config
        configMap:
          name: exostack-hub-config
  volumeClaimTemplates:
  - metadata:
      name: hub-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: standard
      resources:
        requests:
          storage: 10Gi
>>>>>>> 4726b7b (update code for improvement)
---
apiVersion: v1
kind: Service
metadata:
<<<<<<< HEAD
  name: exostack-hub-service
=======
  name: exostack-hub-headless
>>>>>>> 4726b7b (update code for improvement)
  namespace: exostack
  labels:
    app: exostack-hub
spec:
<<<<<<< HEAD
  type: LoadBalancer
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
=======
  clusterIP: None
  ports:
  - port: 8000
    name: http
  - port: 9000
    name: metrics
>>>>>>> 4726b7b (update code for improvement)
  selector:
    app: exostack-hub
---
apiVersion: v1
<<<<<<< HEAD
kind: PersistentVolumeClaim
metadata:
  name: models-pvc
  namespace: exostack
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
=======
kind: Service
metadata:
  name: exostack-hub
  namespace: exostack
  labels:
    app: exostack-hub
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    name: http
  - port: 9000
    targetPort: metrics
    name: metrics
  selector:
    app: exostack-hub
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: exostack-hub-hpa
  namespace: exostack
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: exostack-hub
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 1
        periodSeconds: 120
>>>>>>> 4726b7b (update code for improvement)
