apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: exostack-hub
  namespace: exostack
  labels:
    app: exostack-hub
    component: hub
spec:
  serviceName: exostack-hub-headless
  replicas: 3
  selector:
    matchLabels:
      app: exostack-hub
  template:
    metadata:
      labels:
        app: exostack-hub
        component: hub
    spec:
      serviceAccountName: exostack-sa
      containers:
      - name: hub
        image: exostack/hub:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 9000
          name: metrics
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: exostack-config
        - secretRef:
            name: exostack-secrets
        resources:
          requests:
            cpu: "1"
            memory: "2Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        volumeMounts:
        - name: hub-data
          mountPath: /data
        - name: hub-config
          mountPath: /etc/exostack
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: hub-config
        configMap:
          name: exostack-hub-config
  volumeClaimTemplates:
  - metadata:
      name: hub-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: standard
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: exostack-hub-headless
  namespace: exostack
  labels:
    app: exostack-hub
spec:
  clusterIP: None
  ports:
  - port: 8000
    name: http
  - port: 9000
    name: metrics
  selector:
    app: exostack-hub
---
apiVersion: v1
kind: Service
metadata:
  name: exostack-hub
  namespace: exostack
  labels:
    app: exostack-hub
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: http
    name: http
  - port: 9000
    targetPort: metrics
    name: metrics
  selector:
    app: exostack-hub
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: exostack-hub-hpa
  namespace: exostack
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: exostack-hub
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 1
        periodSeconds: 120