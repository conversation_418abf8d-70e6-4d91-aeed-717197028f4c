apiVersion: v1
kind: ResourceQuota
metadata:
  name: exostack-quota
  namespace: exostack
spec:
  hard:
    requests.cpu: "100"
    requests.memory: 200Gi
    requests.nvidia.com/gpu: "16"
    limits.cpu: "200"
    limits.memory: 400Gi
    limits.nvidia.com/gpu: "16"
    pods: "50"
    services: "25"
    persistentvolumeclaims: "30"
    requests.storage: "1000Gi"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: exostack-limits
  namespace: exostack
spec:
  limits:
  - type: Container
    default:
      cpu: "1"
      memory: 2Gi
    defaultRequest:
      cpu: "0.5"
      memory: 1Gi
    max:
      cpu: "4"
      memory: 16Gi
    min:
      cpu: "0.1"
      memory: 128Mi
  - type: PersistentVolumeClaim
    max:
      storage: 500Gi
    min:
      storage: 1Gi
