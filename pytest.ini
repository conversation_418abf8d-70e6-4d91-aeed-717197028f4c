[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests that require more time
    gpu: Tests that require GPU
    redis: Tests that require Redis
    model: Tests that require model loading
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
