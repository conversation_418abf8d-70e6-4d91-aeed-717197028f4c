<<<<<<< HEAD
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
typer>=0.9.0
aiohttp>=3.9.0
httpx>=0.25.0
pydantic>=2.5.0
transformers>=4.35.0
torch>=2.1.0
redis>=5.0.0
requests>=2.31.0
python-dotenv>=1.0.0
logging-config>=1.0.3
numpy>=1.24.0
scipy>=1.11.0
psutil>=5.9.0
click>=8.1.0
jinja2>=3.1.0
python-multipart>=0.0.6

# Testing dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
pytest-cov>=4.1.0
pytest-xdist>=3.3.0
httpx>=0.25.0  # For testing FastAPI

# Additional AI/ML dependencies
bitsandbytes>=0.41.0  # For 8-bit quantization
accelerate>=0.23.0    # For model loading optimization
safetensors>=0.4.0    # Safer model serialization
sentencepiece>=0.1.99 # For tokenization
protobuf>=4.24.0      # Required by some models

# CLI dependencies
click>=8.1.0
rich>=13.6.0
colorama>=0.4.6  # For Windows color support
=======
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
redis==5.0.1
requests==2.31.0
typer==0.9.0
rich==13.7.0
click==8.1.7
aiohttp==3.9.1
psutil==5.9.6
python-dotenv==1.0.0
transformers==4.36.0
torch==2.1.0
numpy==1.24.3
pytest==7.4.3
pytest-asyncio==0.21.1

>>>>>>> 4726b7b (update code for improvement)
