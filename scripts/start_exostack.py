#!/usr/bin/env python3
"""
ExoStack Startup Script
Starts the hub and agent services for testing and development
"""
import os
import sys
import time
import subprocess
import signal
import logging
from pathlib import Path
from typing import List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExoStackLauncher:
    """Launcher for ExoStack services"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.project_root = project_root
        
    def start_hub(self, port: int = 8000) -> subprocess.Popen:
        """Start the ExoStack hub"""
        logger.info(f"Starting ExoStack Hub on port {port}...")
        
        cmd = [
            sys.executable, "-m", "uvicorn",
            "exo_hub.main:app",
            "--host", "0.0.0.0",
            "--port", str(port),
            "--reload"
        ]
        
        process = subprocess.Popen(
            cmd,
            cwd=self.project_root,
            env=dict(os.environ, PYTHONPATH=str(self.project_root))
        )
        
        self.processes.append(process)
        return process
    
    def start_agent(self, port: int = 8001, agent_id: str = "agent-001") -> subprocess.Popen:
        """Start an ExoStack agent"""
        logger.info(f"Starting ExoStack Agent {agent_id} on port {port}...")
        
        cmd = [
            sys.executable, "-m", "uvicorn",
            "exo_agent.main:app",
            "--host", "0.0.0.0",
            "--port", str(port),
            "--reload"
        ]
        
        env = dict(os.environ)
        env.update({
            "PYTHONPATH": str(self.project_root),
            "AGENT_ID": agent_id,
            "AGENT_PORT": str(port),
            "HUB_URL": "http://localhost:8000"
        })
        
        process = subprocess.Popen(
            cmd,
            cwd=self.project_root,
            env=env
        )
        
        self.processes.append(process)
        return process
    
    def wait_for_service(self, url: str, timeout: int = 30) -> bool:
        """Wait for a service to become available"""
        import requests
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{url}/health", timeout=5)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            time.sleep(1)
        
        return False
    
    def start_all(self, num_agents: int = 1):
        """Start hub and agents"""
        logger.info("🚀 Starting ExoStack distributed AI platform...")
        
        # Start hub
        hub_process = self.start_hub()
        
        # Wait for hub to be ready
        logger.info("⏳ Waiting for hub to start...")
        if not self.wait_for_service("http://localhost:8000"):
            logger.error("❌ Hub failed to start within timeout")
            self.cleanup()
            return False
        
        logger.info("✅ Hub started successfully")
        
        # Start agents
        agent_processes = []
        for i in range(num_agents):
            agent_id = f"agent-{i+1:03d}"
            port = 8001 + i
            agent_process = self.start_agent(port, agent_id)
            agent_processes.append(agent_process)
            
            # Wait for agent to be ready
            logger.info(f"⏳ Waiting for {agent_id} to start...")
            if not self.wait_for_service(f"http://localhost:{port}"):
                logger.error(f"❌ {agent_id} failed to start within timeout")
                continue
            
            logger.info(f"✅ {agent_id} started successfully on port {port}")
            
            # Small delay between agents
            time.sleep(2)
        
        logger.info("🎉 ExoStack platform started successfully!")
        logger.info("📊 Services running:")
        logger.info("   Hub: http://localhost:8000")
        for i in range(num_agents):
            logger.info(f"   Agent {i+1}: http://localhost:{8001+i}")
        
        logger.info("\n🔗 Useful endpoints:")
        logger.info("   Hub Health: http://localhost:8000/health")
        logger.info("   Hub Docs: http://localhost:8000/docs")
        logger.info("   Agent Health: http://localhost:8001/health")
        logger.info("   Agent Docs: http://localhost:8001/docs")
        
        return True
    
    def cleanup(self):
        """Clean up all processes"""
        logger.info("🧹 Cleaning up processes...")
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            except Exception as e:
                logger.warning(f"Error cleaning up process: {e}")
        
        self.processes.clear()
        logger.info("✅ Cleanup completed")
    
    def run_tests(self):
        """Run the test suite"""
        logger.info("🧪 Running ExoStack test suite...")
        
        try:
            # Import and run tests
            from tests.test_exostack_features import TestExoStackFeatures
            
            tester = TestExoStackFeatures()
            tester.setup()
            results = tester.run_comprehensive_test()
            
            return results
            
        except ImportError as e:
            logger.error(f"❌ Failed to import test suite: {e}")
            logger.info("💡 Make sure to install test dependencies: pip install pytest requests")
            return None
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            return None

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info("\n🛑 Received shutdown signal, cleaning up...")
    launcher.cleanup()
    sys.exit(0)

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ExoStack Platform Launcher")
    parser.add_argument("--agents", type=int, default=1, help="Number of agents to start")
    parser.add_argument("--test", action="store_true", help="Run tests after startup")
    parser.add_argument("--test-only", action="store_true", help="Only run tests (assume services are running)")
    
    args = parser.parse_args()
    
    global launcher
    launcher = ExoStackLauncher()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.test_only:
            # Just run tests
            results = launcher.run_tests()
            if results:
                logger.info("✅ Tests completed successfully")
            else:
                logger.error("❌ Tests failed")
                sys.exit(1)
        else:
            # Start services
            success = launcher.start_all(args.agents)
            
            if not success:
                logger.error("❌ Failed to start ExoStack platform")
                sys.exit(1)
            
            if args.test:
                # Wait a bit for services to fully initialize
                logger.info("⏳ Waiting for services to fully initialize...")
                time.sleep(5)
                
                # Run tests
                results = launcher.run_tests()
                if results:
                    logger.info("✅ Tests completed successfully")
                else:
                    logger.error("❌ Tests failed")
            
            # Keep running
            logger.info("🔄 Services running. Press Ctrl+C to stop.")
            
            try:
                while True:
                    time.sleep(1)
                    # Check if processes are still running
                    for process in launcher.processes:
                        if process.poll() is not None:
                            logger.warning(f"⚠️  Process {process.pid} has stopped")
            except KeyboardInterrupt:
                pass
    
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)
    
    finally:
        launcher.cleanup()

if __name__ == "__main__":
    main()
