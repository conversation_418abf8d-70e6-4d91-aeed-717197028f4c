# ExoStack Model Registry
# This file defines available models and their configurations for distributed inference

models:
  # Small/Test Models
  tinyllama:
    hf_repo: "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
    type: "causal_lm"
    size_gb: 2.2
    min_ram_gb: 4
    recommended_ram_gb: 8
    quantized_versions:
      - "4bit"
      - "8bit"
    supports_gpu: true
    supports_cpu: true
    description: "1B parameter model for testing and lightweight inference"
    tags: ["small", "test", "chat"]

  phi2:
    hf_repo: "microsoft/phi-2"
    type: "causal_lm"
    size_gb: 5.4
    min_ram_gb: 8
    recommended_ram_gb: 16
    quantized_versions:
      - "4bit"
      - "8bit"
    supports_gpu: true
    supports_cpu: true
    description: "2.7B parameter model for fast CPU/GPU inference"
    tags: ["small", "fast", "microsoft"]

  # Medium Models
  mistral_7b:
    hf_repo: "mistralai/Mistral-7B-Instruct-v0.2"
    type: "causal_lm"
    size_gb: 14.5
    min_ram_gb: 16
    recommended_ram_gb: 32
    quantized_versions:
      - "4bit"
      - "8bit"
      - "gptq"
    supports_gpu: true
    supports_cpu: false
    description: "7B parameter instruction-tuned model"
    tags: ["medium", "instruct", "mistral"]

  mistral_7b_q4:
    hf_repo: "TheBloke/Mistral-7B-Instruct-v0.2-GGUF"
    type: "causal_lm"
    size_gb: 4.1
    min_ram_gb: 8
    recommended_ram_gb: 16
    quantized_versions:
      - "q4_0"
      - "q4_1"
      - "q5_0"
    supports_gpu: true
    supports_cpu: true
    description: "Quantized 7B parameter model for efficient inference"
    tags: ["medium", "quantized", "gguf"]

  # Specialized Models
  esm3:
    hf_repo: "facebook/esm2_t33_650M_UR50D"
    type: "protein_lm"
    size_gb: 2.5
    min_ram_gb: 6
    recommended_ram_gb: 12
    quantized_versions:
      - "8bit"
    supports_gpu: true
    supports_cpu: true
    description: "Protein language model for biological sequences"
    tags: ["bio", "protein", "specialized"]

  glm4_6b:
    hf_repo: "THUDM/glm-4-9b-chat"
    type: "causal_lm"
    size_gb: 18.2
    min_ram_gb: 20
    recommended_ram_gb: 40
    quantized_versions:
      - "4bit"
      - "8bit"
    supports_gpu: true
    supports_cpu: false
    description: "Chinese/English multilingual model"
    tags: ["multilingual", "chinese", "chat"]

  # Large Models
  llama2_13b:
    hf_repo: "meta-llama/Llama-2-13b-chat-hf"
    type: "causal_lm"
    size_gb: 26.0
    min_ram_gb: 32
    recommended_ram_gb: 64
    quantized_versions:
      - "4bit"
      - "8bit"
      - "gptq"
    supports_gpu: true
    supports_cpu: false
    description: "13B parameter chat model requiring GPU"
    tags: ["large", "chat", "llama"]

  # Default fallback model
  dialogpt_medium:
    hf_repo: "microsoft/DialoGPT-medium"
    type: "causal_lm"
    size_gb: 1.4
    min_ram_gb: 3
    recommended_ram_gb: 6
    quantized_versions:
      - "8bit"
    supports_gpu: true
    supports_cpu: true
    description: "Default conversational model"
    tags: ["default", "conversation", "microsoft"]

# Model categories for easy filtering
categories:
  small_models: ["tinyllama", "phi2", "dialogpt_medium"]
  medium_models: ["mistral_7b", "mistral_7b_q4", "glm4_6b"]
  large_models: ["llama2_13b"]
  cpu_compatible: ["tinyllama", "phi2", "mistral_7b_q4", "esm3", "dialogpt_medium"]
  gpu_only: ["mistral_7b", "glm4_6b", "llama2_13b"]
  quantized_available: ["tinyllama", "phi2", "mistral_7b", "mistral_7b_q4", "glm4_6b", "llama2_13b"]
  specialized: ["esm3"]

# Default configurations
defaults:
  quantization: "8bit"
  torch_dtype: "auto"
  trust_remote_code: true
  use_cache: true
  device_map: "auto"
