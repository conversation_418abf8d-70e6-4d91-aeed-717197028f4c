"""
Comprehensive test suite for ExoStack distributed AI orchestration platform
Tests all implemented features including model registry, GPU routing, and streaming
"""
import asyncio
import json
import time
import requests
import pytest
from typing import Dict, Any, List
from datetime import datetime

# Test configuration
HUB_URL = "http://localhost:8000"
AGENT_URL = "http://localhost:8001"

class TestExoStackFeatures:
    """Test suite for ExoStack features"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment"""
        self.hub_url = HUB_URL
        self.agent_url = AGENT_URL
        self.test_models = [
            "microsoft/DialoGPT-small",  # Small model for testing
            "TinyLlama/TinyLlama-1.1B-Chat-v1.0",
            "microsoft/phi-2"
        ]
    
    def test_hub_health(self):
        """Test hub health endpoint"""
        response = requests.get(f"{self.hub_url}/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        print("✓ Hub health check passed")
    
    def test_agent_health(self):
        """Test agent health endpoint"""
        response = requests.get(f"{self.agent_url}/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "agent_id" in data
        print("✓ Agent health check passed")
    
    def test_agent_detailed_health(self):
        """Test agent detailed health with GPU info"""
        response = requests.get(f"{self.agent_url}/health/detailed")
        assert response.status_code == 200
        data = response.json()
        
        # Check required fields
        assert "system" in data
        assert "resources" in data
        assert "gpu" in data
        assert "capabilities" in data
        
        # Check GPU info structure
        gpu_info = data["gpu"]
        assert "available" in gpu_info
        assert "count" in gpu_info
        assert "devices" in gpu_info
        
        print(f"✓ Agent detailed health check passed")
        print(f"  GPU Available: {gpu_info['available']}")
        print(f"  GPU Count: {gpu_info['count']}")
        
        return data
    
    def test_model_registry_loading(self):
        """Test model registry and available models"""
        response = requests.get(f"{self.agent_url}/models/available")
        assert response.status_code == 200
        data = response.json()
        
        assert "models" in data
        assert len(data["models"]) > 0
        
        # Check for test models
        model_ids = [model["model_id"] for model in data["models"]]
        print(f"✓ Model registry loaded with {len(model_ids)} models")
        print(f"  Available models: {model_ids[:5]}...")  # Show first 5
        
        return data
    
    def test_agent_capabilities(self):
        """Test agent capabilities endpoint"""
        response = requests.get(f"{self.agent_url}/capabilities")
        assert response.status_code == 200
        data = response.json()
        
        # Check capabilities structure
        assert "inference" in data["capabilities"]
        assert "compute_resources" in data
        assert "supported_models" in data
        
        print("✓ Agent capabilities check passed")
        print(f"  Supported models: {len(data['supported_models'])}")
        print(f"  GPU capable: {data['compute_resources']['has_gpu']}")
        
        return data
    
    def test_basic_inference(self):
        """Test basic inference functionality"""
        task_data = {
            "id": f"test_basic_{int(time.time())}",
            "model": "auto",
            "input": "Hello, how are you?",
            "parameters": {
                "max_tokens": 50,
                "temperature": 0.7
            }
        }
        
        response = requests.post(f"{self.agent_url}/inference", json=task_data)
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] in ["completed", "failed"]
        if data["status"] == "completed":
            assert "result" in data
            print("✓ Basic inference test passed")
            print(f"  Generated text length: {len(data['result'].get('output', ''))}")
        else:
            print(f"⚠ Basic inference failed: {data.get('error', 'Unknown error')}")
        
        return data
    
    def test_gpu_aware_task_creation(self):
        """Test GPU-aware task creation"""
        task_data = {
            "model": "auto",
            "input_text": "Explain quantum computing",
            "requires_gpu": True,
            "min_ram_gb": 2.0,
            "min_gpu_memory_gb": 4.0,
            "priority": "normal"
        }
        
        response = requests.post(f"{self.hub_url}/tasks/create_gpu_aware", params=task_data)
        
        if response.status_code == 200:
            data = response.json()
            assert "task_id" in data
            assert data["status"] == "queued"
            print("✓ GPU-aware task creation passed")
            print(f"  Task ID: {data['task_id']}")
            return data
        elif response.status_code == 400:
            print("⚠ GPU-aware task creation failed: No suitable nodes")
            return None
        else:
            pytest.fail(f"Unexpected response: {response.status_code}")
    
    def test_queue_status(self):
        """Test task queue status"""
        response = requests.get(f"{self.hub_url}/tasks/queue/status")
        assert response.status_code == 200
        data = response.json()
        
        assert "queued_tasks" in data
        assert "running_tasks" in data
        
        print("✓ Queue status check passed")
        print(f"  Queued tasks: {data['queued_tasks']}")
        print(f"  Running tasks: {data['running_tasks']}")
        
        return data
    
    def test_streaming_inference_agent(self):
        """Test streaming inference on agent"""
        task_data = {
            "id": f"test_stream_{int(time.time())}",
            "model": "auto",
            "input": "Write a short story about",
            "parameters": {
                "max_tokens": 100,
                "temperature": 0.8
            }
        }
        
        try:
            response = requests.post(
                f"{self.agent_url}/inference/stream",
                json=task_data,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                chunks_received = 0
                total_text = ""
                
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            try:
                                chunk_data = json.loads(line_str[6:])  # Remove 'data: '
                                chunks_received += 1
                                
                                if chunk_data.get("status") == "streaming":
                                    if "token" in chunk_data:
                                        total_text += chunk_data["token"]
                                elif chunk_data.get("status") == "completed":
                                    break
                                elif chunk_data.get("status") == "error":
                                    print(f"⚠ Streaming error: {chunk_data.get('error')}")
                                    return False
                                    
                            except json.JSONDecodeError:
                                continue
                
                print("✓ Streaming inference test passed")
                print(f"  Chunks received: {chunks_received}")
                print(f"  Total text length: {len(total_text)}")
                return True
            else:
                print(f"⚠ Streaming inference failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"⚠ Streaming inference error: {e}")
            return False
    
    def test_model_loading_unloading(self):
        """Test manual model loading and unloading"""
        # Try to load a small model
        test_model = "microsoft/DialoGPT-small"
        
        # Load model
        response = requests.post(
            f"{self.agent_url}/models/load",
            json={"model_id": test_model}
        )
        
        if response.status_code == 200:
            print(f"✓ Model loading test passed for {test_model}")
            
            # Check model status
            status_response = requests.get(f"{self.agent_url}/models/status")
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"  Loaded models: {list(status_data.get('loaded_models', {}).keys())}")
            
            # Unload model
            unload_response = requests.post(
                f"{self.agent_url}/models/unload",
                json={"model_id": test_model}
            )
            
            if unload_response.status_code == 200:
                print(f"✓ Model unloading test passed for {test_model}")
                return True
            else:
                print(f"⚠ Model unloading failed: {unload_response.status_code}")
                return False
        else:
            print(f"⚠ Model loading failed: {response.status_code}")
            return False
    
    def run_comprehensive_test(self):
        """Run all tests in sequence"""
        print("🚀 Starting ExoStack comprehensive test suite...")
        print("=" * 60)
        
        results = {}
        
        try:
            # Basic connectivity tests
            print("\n📡 Testing basic connectivity...")
            results["hub_health"] = self.test_hub_health()
            results["agent_health"] = self.test_agent_health()
            
            # Feature tests
            print("\n🔍 Testing core features...")
            results["detailed_health"] = self.test_agent_detailed_health()
            results["model_registry"] = self.test_model_registry_loading()
            results["capabilities"] = self.test_agent_capabilities()
            
            # Inference tests
            print("\n🧠 Testing inference capabilities...")
            results["basic_inference"] = self.test_basic_inference()
            results["model_loading"] = self.test_model_loading_unloading()
            
            # Advanced features
            print("\n⚡ Testing advanced features...")
            results["gpu_aware_task"] = self.test_gpu_aware_task_creation()
            results["queue_status"] = self.test_queue_status()
            results["streaming"] = self.test_streaming_inference_agent()
            
            print("\n" + "=" * 60)
            print("🎉 Test suite completed!")
            
            # Summary
            passed = sum(1 for result in results.values() if result not in [False, None])
            total = len(results)
            print(f"📊 Results: {passed}/{total} tests passed")
            
            return results
            
        except Exception as e:
            print(f"❌ Test suite failed with error: {e}")
            return {"error": str(e)}

def main():
    """Main test runner"""
    tester = TestExoStackFeatures()
    tester.setup()
    results = tester.run_comprehensive_test()
    
    # Print final summary
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        if result is True:
            status = "✅ PASSED"
        elif result is False:
            status = "❌ FAILED"
        elif result is None:
            status = "⚠️  SKIPPED"
        else:
            status = "✅ PASSED"
        
        print(f"{test_name:20} : {status}")

if __name__ == "__main__":
    main()
