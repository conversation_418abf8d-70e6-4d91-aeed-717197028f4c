{"name": "exostack-dashboard", "version": "1.0.0", "description": "ExoStack Web Dashboard - Real-time monitoring and management interface", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "recharts": "^2.8.0", "axios": "^1.6.0", "react-router-dom": "^6.8.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}