import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Sidebar } from './components/Sidebar';
import { Dashboard } from './pages/Dashboard';
import { TaskQueue } from './pages/TaskQueue';
import { NodeHealth } from './pages/NodeHealth';
import { TaskHistory } from './pages/TaskHistory';
import { ModelRegistry } from './pages/ModelRegistry';
import { Settings } from './pages/Settings';
import './index.css';

function App() {
  return (
    <Router>
      <div className="flex h-screen bg-gray-50">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/tasks" element={<TaskQueue />} />
            <Route path="/nodes" element={<NodeHealth />} />
            <Route path="/history" element={<TaskHistory />} />
            <Route path="/models" element={<ModelRegistry />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
