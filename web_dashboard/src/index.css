@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-sans antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .card-header {
    @apply flex items-center justify-between mb-4;
  }
  
  .card-title {
    @apply text-lg font-semibold text-gray-900;
  }
  
  .card-subtitle {
    @apply text-sm text-gray-500;
  }
  
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-success {
    @apply btn text-white bg-success-600 hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn text-white bg-warning-600 hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn text-white bg-danger-600 hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-active {
    @apply status-badge bg-success-100 text-success-800;
  }
  
  .status-inactive {
    @apply status-badge bg-gray-100 text-gray-800;
  }
  
  .status-pending {
    @apply status-badge bg-warning-100 text-warning-800;
  }
  
  .status-running {
    @apply status-badge bg-primary-100 text-primary-800;
  }
  
  .status-completed {
    @apply status-badge bg-success-100 text-success-800;
  }
  
  .status-failed {
    @apply status-badge bg-danger-100 text-danger-800;
  }
  
  .metric-card {
    @apply card;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-gray-900;
  }
  
  .metric-label {
    @apply text-sm text-gray-500 mt-1;
  }
  
  .metric-change {
    @apply text-sm font-medium;
  }
  
  .metric-change-positive {
    @apply metric-change text-success-600;
  }
  
  .metric-change-negative {
    @apply metric-change text-danger-600;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }
  
  .progress-fill {
    @apply h-2 rounded-full transition-all duration-300;
  }
  
  .progress-fill-primary {
    @apply progress-fill bg-primary-600;
  }
  
  .progress-fill-success {
    @apply progress-fill bg-success-600;
  }
  
  .progress-fill-warning {
    @apply progress-fill bg-warning-600;
  }
  
  .progress-fill-danger {
    @apply progress-fill bg-danger-600;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
