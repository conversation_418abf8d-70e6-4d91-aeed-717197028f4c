import React, { useState, useEffect } from 'react';
import { 
  Server, 
  ListTodo, 
  CheckCircle, 
  AlertCircle, 
  TrendingUp,
  Activity,
  Cpu,
  HardDrive
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

export function Dashboard() {
  const [stats, setStats] = useState({
    nodes: { active: 0, total: 0 },
    tasks: { pending: 0, running: 0, completed: 0, failed: 0 },
    performance: { avgTokensPerSec: 0, totalTokens: 0 }
  });
  
  const [chartData, setChartData] = useState([]);
  const [nodeHealthData, setNodeHealthData] = useState([]);

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch stats from API
      const response = await fetch('/api/stats/dashboard');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats || stats);
        setChartData(data.chartData || generateMockChartData());
        setNodeHealthData(data.nodeHealth || generateMockNodeHealth());
      } else {
        // Use mock data if API not available
        setChartData(generateMockChartData());
        setNodeHealthData(generateMockNodeHealth());
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      // Use mock data
      setChartData(generateMockChartData());
      setNodeHealthData(generateMockNodeHealth());
    }
  };

  const generateMockChartData = () => {
    const now = new Date();
    return Array.from({ length: 24 }, (_, i) => ({
      time: new Date(now.getTime() - (23 - i) * 60 * 60 * 1000).toLocaleTimeString('en-US', { hour: '2-digit' }),
      tasks: Math.floor(Math.random() * 50) + 10,
      tokens: Math.floor(Math.random() * 1000) + 200,
      nodes: Math.floor(Math.random() * 5) + 2
    }));
  };

  const generateMockNodeHealth = () => {
    return [
      { name: 'node-gpu-01', cpu: 45, memory: 67, gpu: 78, status: 'active' },
      { name: 'node-cpu-02', cpu: 23, memory: 34, gpu: 0, status: 'active' },
      { name: 'node-gpu-03', cpu: 89, memory: 92, gpu: 95, status: 'warning' },
      { name: 'node-cpu-04', cpu: 12, memory: 28, gpu: 0, status: 'active' }
    ];
  };

  const MetricCard = ({ title, value, change, icon: Icon, color = 'primary' }) => (
    <div className="metric-card animate-fade-in">
      <div className="card-header">
        <div>
          <p className="metric-label">{title}</p>
          <p className="metric-value">{value}</p>
          {change && (
            <p className={`metric-change ${change > 0 ? 'metric-change-positive' : 'metric-change-negative'}`}>
              {change > 0 ? '+' : ''}{change}%
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  );

  const NodeHealthCard = ({ node }) => {
    const getStatusColor = (status) => {
      switch (status) {
        case 'active': return 'success';
        case 'warning': return 'warning';
        case 'error': return 'danger';
        default: return 'gray';
      }
    };

    const getResourceColor = (usage) => {
      if (usage > 80) return 'danger';
      if (usage > 60) return 'warning';
      return 'success';
    };

    return (
      <div className="card animate-slide-up">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900">{node.name}</h3>
          <span className={`status-${node.status}`}>
            {node.status}
          </span>
        </div>
        
        <div className="space-y-3">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">CPU</span>
              <span className="font-medium">{node.cpu}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className={`progress-fill-${getResourceColor(node.cpu)}`}
                style={{ width: `${node.cpu}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Memory</span>
              <span className="font-medium">{node.memory}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className={`progress-fill-${getResourceColor(node.memory)}`}
                style={{ width: `${node.memory}%` }}
              ></div>
            </div>
          </div>
          
          {node.gpu > 0 && (
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600">GPU</span>
                <span className="font-medium">{node.gpu}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className={`progress-fill-${getResourceColor(node.gpu)}`}
                  style={{ width: `${node.gpu}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">ExoStack distributed AI orchestration overview</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-600">Live Updates</span>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Active Nodes"
          value={`${stats.nodes.active}/${stats.nodes.total}`}
          change={5}
          icon={Server}
          color="primary"
        />
        <MetricCard
          title="Running Tasks"
          value={stats.tasks.running}
          change={-2}
          icon={ListTodo}
          color="warning"
        />
        <MetricCard
          title="Completed Tasks"
          value={stats.tasks.completed}
          change={12}
          icon={CheckCircle}
          color="success"
        />
        <MetricCard
          title="Failed Tasks"
          value={stats.tasks.failed}
          change={-8}
          icon={AlertCircle}
          color="danger"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Task Activity Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Task Activity (24h)</h3>
            <TrendingUp className="w-5 h-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="tasks" 
                stroke="#3b82f6" 
                strokeWidth={2}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Token Generation Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Token Generation (24h)</h3>
            <Activity className="w-5 h-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="tokens" fill="#22c55e" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Node Health Heatmap */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Node Health Overview</h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-success-500 rounded mr-2"></div>
              Healthy
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-warning-500 rounded mr-2"></div>
              Warning
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-danger-500 rounded mr-2"></div>
              Critical
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {nodeHealthData.map((node, index) => (
            <NodeHealthCard key={index} node={node} />
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Recent Activity</h3>
        </div>
        <div className="space-y-3">
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <CheckCircle className="w-5 h-5 text-success-600" />
            <div className="flex-1">
              <p className="text-sm font-medium">Task completed successfully</p>
              <p className="text-xs text-gray-500">TinyLlama inference on node-gpu-01 • 2 minutes ago</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <Server className="w-5 h-5 text-primary-600" />
            <div className="flex-1">
              <p className="text-sm font-medium">New node registered</p>
              <p className="text-xs text-gray-500">node-cpu-05 joined the cluster • 5 minutes ago</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <AlertCircle className="w-5 h-5 text-warning-600" />
            <div className="flex-1">
              <p className="text-sm font-medium">High GPU utilization detected</p>
              <p className="text-xs text-gray-500">node-gpu-03 at 95% capacity • 8 minutes ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
